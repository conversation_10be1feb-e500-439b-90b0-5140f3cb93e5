// User roles
export enum UserRole {
  ADMIN = 'admin',
  SUB_ADMIN = 'sub_admin',
  STUDENT = 'student'
}

// User model
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

// Admin model extends User
export interface Admin extends User {
  role: UserRole.ADMIN;
}

// Sub-Admin model extends User
export interface SubAdmin extends User {
  role: UserRole.SUB_ADMIN;
}

// Student model extends User
export interface Student extends User {
  role: UserRole.STUDENT;
  roll_number: string;
  college_name: string;
  department: string;
}

// Department model
export interface Department {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

// Book model
export interface Book {
  id: string;
  name: string;
  serial_number: string;
  author: string;
  registration_number: string;
  department_id: string;
  rack_location: string;
  is_available: boolean;
  qr_code: string;
  created_at: string;
  updated_at: string;
}

// E-Book model
export interface EBook {
  id: string;
  name: string;
  author: string;
  file_url: string;
  department_id: string;
  created_at: string;
  updated_at: string;
}

// Borrowing record model
export interface BorrowingRecord {
  id: string;
  book_id: string;
  student_id: string;
  borrow_date: string;
  due_date: string;
  return_date: string | null;
  is_returned: boolean;
  created_at: string;
  updated_at: string;
}

// Database schema
export interface Database {
  public: {
    Tables: {
      users: {
        Row: User;
        Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<User, 'id' | 'created_at' | 'updated_at'>>;
      };
      students: {
        Row: Student;
        Insert: Omit<Student, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Student, 'id' | 'created_at' | 'updated_at'>>;
      };
      departments: {
        Row: Department;
        Insert: Omit<Department, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Department, 'id' | 'created_at' | 'updated_at'>>;
      };
      books: {
        Row: Book;
        Insert: Omit<Book, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Book, 'id' | 'created_at' | 'updated_at'>>;
      };
      ebooks: {
        Row: EBook;
        Insert: Omit<EBook, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<EBook, 'id' | 'created_at' | 'updated_at'>>;
      };
      borrowing_records: {
        Row: BorrowingRecord;
        Insert: Omit<BorrowingRecord, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<BorrowingRecord, 'id' | 'created_at' | 'updated_at'>>;
      };
    };
  };
}
