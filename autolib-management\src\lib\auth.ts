import { supabase } from './supabase';
import { UserRole } from '@/types';

// Sign up a new user
export async function signUp(email: string, password: string, name: string, role: UserRole) {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role,
        },
      },
    });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error signing up:', error);
    return { success: false, error };
  }
}

// Sign in a user
export async function signIn(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error signing in:', error);
    return { success: false, error };
  }
}

// Sign out a user
export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    return { success: false, error };
  }
}

// Get the current user
export async function getCurrentUser() {
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      throw error;
    }

    return { success: true, user: data.user };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { success: false, error };
  }
}

// Get the user's role
export async function getUserRole() {
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      throw error;
    }

    return { success: true, role: data.user?.user_metadata?.role };
  } catch (error) {
    console.error('Error getting user role:', error);
    return { success: false, error };
  }
}

// Register a new student
export async function registerStudent(
  email: string,
  password: string,
  name: string,
  rollNumber: string,
  collegeName: string,
  department: string
) {
  try {
    // First create the auth user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role: UserRole.STUDENT,
        },
      },
    });

    if (error) {
      throw error;
    }

    // Then create the student record
    const { error: studentError } = await supabase.from('students').insert({
      id: data.user?.id,
      email,
      name,
      role: UserRole.STUDENT,
      roll_number: rollNumber,
      college_name: collegeName,
      department,
    });

    if (studentError) {
      throw studentError;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error registering student:', error);
    return { success: false, error };
  }
}

// Create a new admin or sub-admin
export async function createStaff(email: string, password: string, name: string, role: UserRole.ADMIN | UserRole.SUB_ADMIN) {
  try {
    // Only admins can create other admins or sub-admins
    const { role: currentUserRole } = await getUserRole();
    
    if (currentUserRole !== UserRole.ADMIN) {
      throw new Error('Only admins can create staff accounts');
    }

    // Create the auth user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role,
        },
      },
    });

    if (error) {
      throw error;
    }

    // Create the user record
    const { error: userError } = await supabase.from('users').insert({
      id: data.user?.id,
      email,
      name,
      role,
    });

    if (userError) {
      throw userError;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error creating staff account:', error);
    return { success: false, error };
  }
}

// Check if user has required role
export function checkRole(userRole: UserRole | null | undefined, requiredRole: UserRole | UserRole[]) {
  if (!userRole) return false;
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(userRole);
  }
  
  return userRole === requiredRole;
}
