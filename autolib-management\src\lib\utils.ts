import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Generate QR code data for a book
export function generateBookQRData(book: {
  id: string;
  name: string;
  serial_number: string;
  author: string;
  registration_number: string;
}) {
  return JSON.stringify({
    id: book.id,
    name: book.name,
    serial_number: book.serial_number,
    author: book.author,
    registration_number: book.registration_number,
    type: 'book'
  });
}

// Parse QR code data
export function parseQRData(qrData: string) {
  try {
    return JSON.parse(qrData);
  } catch (error) {
    console.error('Error parsing QR data:', error);
    return null;
  }
}

// Format date for display
export function formatDate(date: string | Date) {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// Calculate due date (default 14 days from borrow date)
export function calculateDueDate(borrowDate: Date, daysToAdd: number = 14): Date {
  const dueDate = new Date(borrowDate);
  dueDate.setDate(dueDate.getDate() + daysToAdd);
  return dueDate;
}

// Check if book is overdue
export function isOverdue(dueDate: string | Date): boolean {
  const due = new Date(dueDate);
  const now = new Date();
  return now > due;
}

// Generate unique serial number for books
export function generateSerialNumber(prefix: string = 'BK'): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}-${timestamp}-${random}`.toUpperCase();
}

// Generate unique registration number for books
export function generateRegistrationNumber(departmentCode: string): string {
  const year = new Date().getFullYear();
  const timestamp = Date.now().toString(36);
  return `${departmentCode}-${year}-${timestamp}`.toUpperCase();
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate roll number format (customize as needed)
export function isValidRollNumber(rollNumber: string): boolean {
  // Example: 2023CSE001, 2024ECE045, etc.
  const rollRegex = /^\d{4}[A-Z]{2,4}\d{3}$/;
  return rollRegex.test(rollNumber);
}

// Get department code from department name
export function getDepartmentCode(departmentName: string): string {
  const codes: { [key: string]: string } = {
    'Computer Science': 'CSE',
    'Electrical Engineering': 'EEE',
    'Mechanical Engineering': 'MECH',
    'Civil Engineering': 'CIVIL',
    'Electronics and Communication': 'ECE',
    'Information Technology': 'IT',
    'Chemical Engineering': 'CHEM',
    'Biotechnology': 'BT'
  };
  return codes[departmentName] || 'GEN';
}

// Search function for books
export function searchBooks(books: any[], searchTerm: string) {
  if (!searchTerm) return books;
  
  const term = searchTerm.toLowerCase();
  return books.filter(book => 
    book.name.toLowerCase().includes(term) ||
    book.author.toLowerCase().includes(term) ||
    book.serial_number.toLowerCase().includes(term) ||
    book.registration_number.toLowerCase().includes(term)
  );
}

// Filter books by availability
export function filterBooksByAvailability(books: any[], showAvailable: boolean = true) {
  return books.filter(book => book.is_available === showAvailable);
}

// Sort books by different criteria
export function sortBooks(books: any[], sortBy: 'name' | 'author' | 'created_at' = 'name', order: 'asc' | 'desc' = 'asc') {
  return books.sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];
    
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    if (order === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });
}
